
plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id 'com.google.gms.google-services'
    id 'com.huawei.agconnect'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace 'com.busaty.school'
    compileSdkVersion 35
    ndkVersion = flutter.ndkVersion

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    defaultConfig {
        applicationId "com.busaty.school"
        minSdkVersion 23  // Updated from 21 to 23 to support Firebase Auth
        targetSdkVersion 35
        versionCode flutter.versionCode.toInteger()
        versionName flutter.versionName
        multiDexEnabled true
        manifestPlaceholders += [
            recaptchaKey: "6Lf06L4qAAAAAMmz_WFaYxcMyDcLcFIBmhGc4bPn",
            applicationName: "io.flutter.app.FlutterApplication"
        ]
        // REVERSED_CLIENT_ID removed - Flutter gets it automatically from google-services.json
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true // Enable code shrinking
            shrinkResources true // Shrink unused resources
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        debug {
            // Disable PNG crunching for debug builds to speed up build time
            crunchPngs false
        }
    }

    // Split the APK to reduce its size (disabled for now to fix debug builds)
    // splits {
    //     abi {
    //         enable true
    //         reset()
    //         include 'armeabi-v7a', 'arm64-v8a'
    //         universalApk false
    //     }
    // }

    // Remove deprecated dexOptions
    // dexOptions {
    //     javaMaxHeapSize "4g"
    //     preDexLibraries = false
    // }

    // Ensure compatibility with AGP 8.2.1+
    tasks.withType(JavaCompile).configureEach {
        options.fork = true
        options.forkOptions.jvmArgs = ['-Xms512m', '-Xmx4g']
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Import the Firebase BoM
    implementation platform('com.google.firebase:firebase-bom:33.7.0')

    // Add the dependencies for Firebase products without versions

    implementation 'com.google.firebase:firebase-dynamic-links'
    implementation 'com.google.firebase:firebase-messaging:23.0.0'

    // Add Google Play Services dependencies
    implementation 'com.google.android.gms:play-services-safetynet:17.0.0'
    implementation 'com.google.android.gms:play-services-base:18.2.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4' // Use the latest version

    // Add HMS Core dependencies
    implementation 'com.huawei.hms:push:6.11.0.300'

    // Add MultiDex support
    implementation 'androidx.multidex:multidex:2.0.1'
}

// apply plugin: 'com.huawei.agconnect'  // Removed as it's now in the plugins block
