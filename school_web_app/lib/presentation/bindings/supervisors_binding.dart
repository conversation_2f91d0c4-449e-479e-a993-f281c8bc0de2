import 'package:get/get.dart';

/// Supervisors binding for dependency injection
/// Following Single Responsibility Principle by focusing only on supervisors dependencies
class SupervisorsBinding extends Bindings {
  @override
  void dependencies() {
    // The SupervisorsController and its dependencies are already registered in DependencyInjection
    // This binding is used when navigating to supervisor-specific routes
    // We don't need to re-register anything here since all dependencies are globally available

    // If you need to register supervisor-specific dependencies that aren't global,
    // add them here following the pattern of other bindings
  }
}
